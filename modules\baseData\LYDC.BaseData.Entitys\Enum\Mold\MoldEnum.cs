﻿using LYDC.DependencyInjection;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace LYDC.BaseData.Entitys.Enum.Mold;

/// <summary>
/// 审核状态.
/// </summary>
[SuppressSniffer]
public enum MoldReviewStatus
{
    /// <summary>
    /// 待审核.
    /// </summary>
    [Description("待审核")]
    WaitReview = 1,

    /// <summary>
    /// 已审核.
    /// </summary>
    [Description("已审核")]
    Review = 2
}