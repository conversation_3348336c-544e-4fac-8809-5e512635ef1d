﻿using LYDC.DependencyInjection;
using System.ComponentModel;

namespace LYDC.CoreCommon.Entities.Enum.IDGeneratorRule;

/// <summary>
/// ID生成规则排除类型.
/// </summary>
[SuppressSniffer]
public enum IDGeneratorRuleExcludeType
{
    /// <summary>
    /// 全匹配.
    /// </summary>
    [Description("全匹配")]
    ALL = 1,

    /// <summary>
    /// 包含.
    /// </summary>
    [Description("包含")]
    Include = 2,

}
