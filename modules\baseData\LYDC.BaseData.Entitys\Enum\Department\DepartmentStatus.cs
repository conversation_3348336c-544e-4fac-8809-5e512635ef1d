﻿using LYDC.DependencyInjection;
using System.ComponentModel;

namespace LYDC.BaseData.Entitys.Enum.Department
{
    /// <summary>
    /// 部门状态.
    /// </summary>
    [SuppressSniffer]

    public enum DepartmentStatus
    {
        /// <summary>
        /// 启用.
        /// </summary>
        [Description("启用")]
        Enable = 1,

        /// <summary>
        /// 停用.
        /// </summary>
        [Description("停用")]
        Disable = 2
    }
}
