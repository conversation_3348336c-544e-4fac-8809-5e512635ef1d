INSERT INTO public.p_bd_label_template (f_id, label_name, filter_id, label_template, contain_lable_number, is_lotno_increase, is_use_separator, status, remark, f_enabled_mark, f_sort_code, f_creator_time, f_creator_user_id, f_last_modify_time, f_last_modify_user_id, f_delete_time, f_delete_user_id, f_delete_mark, f_tenant_id) VALUES('672248245199568837', '流转标签(维修)', '20250323', '﻿<?xml version="1.0" encoding="utf-8"?>
<Report ScriptLanguage="CSharp" ReportInfo.Created="03/20/2025 11:34:33" ReportInfo.Modified="03/20/2025 14:38:06" ReportInfo.CreatorVersion="2013.2.5.0">
  <Dictionary>
    <Parameter Name="frCds_Print" DataType="System.String">
      <Parameter Name="relationWorkOrderNo" DataType="System.String"/>
      <Parameter Name="customer" DataType="System.String"/>
      <Parameter Name="dieCutMaterials" DataType="System.String"/>
      <Parameter Name="teethQuantity" DataType="System.String"/>
      <Parameter Name="mouldNo" DataType="System.String"/>
      <Parameter Name="sheetQuantity" DataType="System.String"/>
      <Parameter Name="operatorName" DataType="System.String"/>
      <Parameter Name="printUserName" DataType="System.String"/>
      <Parameter Name="productCode" DataType="System.String"/>
      <Parameter Name="creatorTime" DataType="System.String"/>
      <Parameter Name="qrCode" DataType="System.String"/>
      <Parameter Name="title" DataType="System.String"/>
      <Parameter Name="rawMaterialBatchNo" DataType="System.String"/>
      <Parameter Name="creatorUserName" DataType="System.String"/>
      <Parameter Name="documentNumber" DataType="System.String"/>
      <Parameter Name="machineNo" DataType="System.String"/>
      <Parameter Name="batchNo" DataType="System.String"/>
      <Parameter Name="workOrderQuantity" DataType="System.String"/>
      <Parameter Name="workOrderNo" DataType="System.String"/>
      <Parameter Name="SA" DataType="System.String"/>
      <Parameter Name="EN" DataType="System.String"/>
      <Parameter Name="QA" DataType="System.String"/>
      <Parameter Name="customerDeliveryTime" DataType="System.String"/>
      <Parameter Name="demandQty" DataType="System.String"/>
      <Parameter Name="material" DataType="System.String"/>
      <Parameter Name="productDesc" DataType="System.String"/>
      <Parameter Name="immediateCustomerPartNumber" DataType="System.String"/>
      <Parameter Name="insidePartNumber" DataType="System.String"/>
      <Parameter Name="insideProjectCode" DataType="System.String"/>
      <Parameter Name="immediateCustomerCode" DataType="System.String"/>
      <Parameter Name="Waiver" DataType="System.String"/>
      <Parameter Name="PONo" DataType="System.String"/>
      <Parameter Name="Vendor" DataType="System.String"/>
      <Parameter Name="VC" DataType="System.String"/>
      <Parameter Name="PlanNumber" DataType="System.String"/>
      <Parameter Name="OuterBoxId" DataType="System.String"/>
      <Parameter Name="TagGUID" DataType="System.String"/>
      <Parameter Name="ReelNumber" DataType="System.String"/>
      <Parameter Name="MPress" DataType="System.String"/>
      <Parameter Name="PKGID" DataType="System.String"/>
      <Parameter Name="CreateUser" DataType="System.String"/>
      <Parameter Name="NGType" DataType="System.String"/>
      <Parameter Name="Supplier" DataType="System.String"/>
      <Parameter Name="MouldNo" DataType="System.String"/>
      <Parameter Name="CreateTime" DataType="System.String"/>
      <Parameter Name="Result" DataType="System.String"/>
      <Parameter Name="QrCode" DataType="System.String"/>
      <Parameter Name="Type" DataType="System.String"/>
      <Parameter Name="PrintQty" DataType="System.String"/>
      <Parameter Name="Qty" DataType="System.String"/>
      <Parameter Name="DC" DataType="System.String"/>
      <Parameter Name="Lot" DataType="System.String"/>
      <Parameter Name="VendorCode" DataType="System.String"/>
      <Parameter Name="PO" DataType="System.String"/>
      <Parameter Name="MPN" DataType="System.String"/>
      <Parameter Name="PN" DataType="System.String"/>
      <Parameter Name="ReelId" DataType="System.String"/>
      <Parameter Name="Resilience" DataType="System.String"/>
      <Parameter Name="Stick" DataType="System.String"/>
      <Parameter Name="Thickness" DataType="System.String"/>
      <Parameter Name="IsLot26" DataType="System.String"/>
      <Parameter Name="PlanTime" DataType="System.String"/>
      <Parameter Name="WordText" DataType="System.String"/>
      <Parameter Name="MaintainTime" DataType="System.String"/>
      <Parameter Name="MaintainUser" DataType="System.String"/>
      <Parameter Name="Grade" DataType="System.String"/>
      <Parameter Name="PackNumber" DataType="System.String"/>
      <Parameter Name="PalletWeigh" DataType="System.String"/>
      <Parameter Name="LabelNetWeight" DataType="System.String"/>
      <Parameter Name="PackWeigh" DataType="System.String"/>
      <Parameter Name="AGHFKEmpNo" DataType="System.String"/>
      <Parameter Name="AGHLotNo" DataType="System.String"/>
      <Parameter Name="MaterialQuanlity" DataType="System.String"/>
      <Parameter Name="TypeTimeDateConversion" DataType="System.String"/>
      <Parameter Name="WeekNumbSunday" DataType="System.String"/>
      <Parameter Name="WeekNumbMonday" DataType="System.String"/>
      <Parameter Name="DayOfWeekSunday" DataType="System.String"/>
      <Parameter Name="DayOfWeekMonday" DataType="System.String"/>
      <Parameter Name="ColorBin" DataType="System.String"/>
      <Parameter Name="ColorCode" DataType="System.String"/>
      <Parameter Name="Site" DataType="System.String"/>
      <Parameter Name="MRPDesc" DataType="System.String"/>
      <Parameter Name="MRP" DataType="System.String"/>
      <Parameter Name="GUID" DataType="System.String"/>
      <Parameter Name="StorageTime" DataType="System.String"/>
      <Parameter Name="RandomLot" DataType="System.String"/>
      <Parameter Name="DFLotNoC" DataType="System.String"/>
      <Parameter Name="Measures" DataType="System.String"/>
      <Parameter Name="difference" DataType="System.String"/>
      <Parameter Name="ProcessNameType" DataType="System.String"/>
      <Parameter Name="InkjetNumber" DataType="System.String"/>
      <Parameter Name="NoteMatters" DataType="System.String"/>
      <Parameter Name="Fremarks" DataType="System.String"/>
      <Parameter Name="MoldNumber" DataType="System.String"/>
      <Parameter Name="Modulus" DataType="System.String"/>
      <Parameter Name="TwoProdNo" DataType="System.String"/>
      <Parameter Name="LabelID" DataType="System.String"/>
      <Parameter Name="FOperator" DataType="System.String"/>
      <Parameter Name="ReprintState" DataType="System.String"/>
      <Parameter Name="AEProdNo" DataType="System.String"/>
      <Parameter Name="PackCode" DataType="System.String"/>
      <Parameter Name="Betweens" DataType="System.String"/>
      <Parameter Name="DFLC" DataType="System.String"/>
      <Parameter Name="SpecProdNo" DataType="System.String"/>
      <Parameter Name="DFOutQRList" DataType="System.String"/>
      <Parameter Name="DFLotNoB" DataType="System.String"/>
      <Parameter Name="DFLotNoA" DataType="System.String"/>
      <Parameter Name="Address" DataType="System.String"/>
      <Parameter Name="FMaterialLot" DataType="System.String"/>
      <Parameter Name="LotNoByRule" DataType="System.String"/>
      <Parameter Name="FMachine" DataType="System.String"/>
      <Parameter Name="Fteam" DataType="System.String"/>
      <Parameter Name="Tax" DataType="System.String"/>
      <Parameter Name="CartonLotNo" DataType="System.String"/>
      <Parameter Name="LCLotNo" DataType="System.String"/>
      <Parameter Name="LC" DataType="System.String"/>
      <Parameter Name="Process" DataType="System.String"/>
      <Parameter Name="Stage" DataType="System.String"/>
      <Parameter Name="Batch" DataType="System.String"/>
      <Parameter Name="LCProdNo" DataType="System.String"/>
      <Parameter Name="FlineGroup" DataType="System.String"/>
      <Parameter Name="Config34" DataType="System.String"/>
      <Parameter Name="CustProdNo2" DataType="System.String"/>
      <Parameter Name="CustProName" DataType="System.String"/>
      <Parameter Name="D17Config" DataType="System.String"/>
      <Parameter Name="Description2" DataType="System.String"/>
      <Parameter Name="Description1" DataType="System.String"/>
      <Parameter Name="Expand2" DataType="System.String"/>
      <Parameter Name="Expand1" DataType="System.String"/>
      <Parameter Name="DayOfWeek" DataType="System.String"/>
      <Parameter Name="FProcessTransID" DataType="System.String"/>
      <Parameter Name="BatchNoDateConversion" DataType="System.String"/>
      <Parameter Name="FplanQty" DataType="System.String"/>
      <Parameter Name="UFname" DataType="System.String"/>
      <Parameter Name="FClass" DataType="System.String"/>
      <Parameter Name="FRemark" DataType="System.String"/>
      <Parameter Name="SeriaDateTime" DataType="System.String"/>
      <Parameter Name="SerialNo" DataType="System.String"/>
      <Parameter Name="MachineOperator" DataType="System.String"/>
      <Parameter Name="OperatorName" DataType="System.String"/>
      <Parameter Name="TeamName" DataType="System.String"/>
      <Parameter Name="Manuorder" DataType="System.String"/>
      <Parameter Name="OwnProdNo_Barcode" DataType="System.String"/>
      <Parameter Name="CustProdNo_Barcode" DataType="System.String"/>
      <Parameter Name="Tag" DataType="System.String"/>
      <Parameter Name="FilterID" DataType="System.String"/>
      <Parameter Name="CompID" DataType="System.String"/>
      <Parameter Name="CaseSpec" DataType="System.String"/>
      <Parameter Name="BoxSpec" DataType="System.String"/>
      <Parameter Name="ProdSpec" DataType="System.String"/>
      <Parameter Name="PaperBatchNo" DataType="System.String"/>
      <Parameter Name="BatchNo" DataType="System.String"/>
      <Parameter Name="ItemName" DataType="System.String"/>
      <Parameter Name="LimitDate" DataType="System.String"/>
      <Parameter Name="WeekNumb" DataType="System.String"/>
      <Parameter Name="LotNumb" DataType="System.String"/>
      <Parameter Name="LotNo104" DataType="System.String"/>
      <Parameter Name="Config" DataType="System.String"/>
      <Parameter Name="PackCount" DataType="System.String"/>
      <Parameter Name="FKEmpNo" DataType="System.String"/>
      <Parameter Name="RevNo" DataType="System.String"/>
      <Parameter Name="ProDes" DataType="System.String"/>
      <Parameter Name="ProdDesc" DataType="System.String"/>
      <Parameter Name="LastProdNo" DataType="System.String"/>
      <Parameter Name="CustProdNo" DataType="System.String"/>
      <Parameter Name="OwnProdNo" DataType="System.String"/>
      <Parameter Name="TransformOrder" DataType="System.String"/>
      <Parameter Name="LeaveMaterial" DataType="System.String"/>
      <Parameter Name="VolumeQuantity" DataType="System.String"/>
      <Parameter Name="Specifications" DataType="System.String"/>
      <Parameter Name="DemandedQuantity" DataType="System.String"/>
      <Parameter Name="BranchQuantity" DataType="System.String"/>
      <Parameter Name="WorkOrderNo" DataType="System.String"/>
      <Parameter Name="FactoryCode" DataType="System.String"/>
      <Parameter Name="UpdateTime" DataType="System.String"/>
      <Parameter Name="UpdateUser" DataType="System.String"/>
      <Parameter Name="FactoryName" DataType="System.String"/>
      <Parameter Name="Material" DataType="System.String"/>
      <Parameter Name="PrintDate" DataType="System.String"/>
      <Parameter Name="LotNo" DataType="System.String"/>
      <Parameter Name="BoxQty" DataType="System.String"/>
      <Parameter Name="PackQty" DataType="System.String"/>
      <Parameter Name="Customer" DataType="System.String"/>
    </Parameter>
  </Dictionary>
  <ReportPage Name="Page1" Landscape="true" PaperWidth="100" PaperHeight="60" RawPaperSize="256" LeftMargin="0" TopMargin="0" RightMargin="0" BottomMargin="0" FirstPageSource="256" OtherPagesSource="256">
    <DataBand Name="Data1" Width="378" Height="226.8">
      <TableObject Name="Table1" Left="18.9" Top="9.45" Width="340.2" Height="207.72" Border.Lines="All">
        <TableColumn Name="Column1"/>
        <TableColumn Name="Column2" Width="160.65"/>
        <TableColumn Name="Column3" Width="113.4"/>
        <TableRow Name="Row1" Height="34.62">
          <TableCell Name="Cell1" Border.Lines="All" Text="[frCds_Print.title]" HorzAlign="Center" VertAlign="Center" Font="宋体, 9pt" ColSpan="3"/>
          <TableCell Name="Cell2" Border.Lines="Left, Top" HorzAlign="Center" VertAlign="Center" Font="宋体, 9pt"/>
          <TableCell Name="Cell3" Border.Lines="Left, Right, Bottom" HorzAlign="Center" VertAlign="Center" Font="宋体, 9pt"/>
        </TableRow>
        <TableRow Name="Row2" Height="34.62">
          <TableCell Name="Cell6" Border.Lines="All" Text="模具编号" HorzAlign="Center" VertAlign="Center" Font="宋体, 9pt"/>
          <TableCell Name="Cell7" Border.Lines="Left, Top" Text="[frCds_Print.mouldNo]" VertAlign="Center" Font="宋体, 9pt"/>
          <TableCell Name="Cell8" Border.Lines="Left, Right, Bottom" HorzAlign="Right" Font="宋体, 9pt" RowSpan="4">
            <BarcodeObject Name="Barcode1" Left="9.45" Top="9.45" Width="97.1" Height="106.55" AutoSize="false" Expression="[frCds_Print.qrCode]" ShowText="false" Padding="1, 1, 1, 1" Barcode="QR Code" Barcode.ErrorCorrection="L" Barcode.Encoding="UTF8" Barcode.QuietZone="true"/>
          </TableCell>
        </TableRow>
        <TableRow Name="Row3" Height="34.62">
          <TableCell Name="Cell11" Border.Lines="All" Text="齿数" HorzAlign="Center" VertAlign="Center" Font="宋体, 9pt"/>
          <TableCell Name="Cell12" Border.Lines="Left, Top" Text="[frCds_Print.teethQuantity]" VertAlign="Center" Font="宋体, 9pt"/>
          <TableCell Name="Cell13" Border.Lines="Left, Right, Bottom" HorzAlign="Right" Font="宋体, 9pt"/>
        </TableRow>
        <TableRow Name="Row4" Height="34.62">
          <TableCell Name="Cell16" Border.Lines="All" Text="关联工单" HorzAlign="Center" VertAlign="Center" Font="宋体, 9pt"/>
          <TableCell Name="Cell17" Border.Lines="Left, Top" Text="[frCds_Print.relationWorkOrderNo]" VertAlign="Center" Font="宋体, 9pt"/>
          <TableCell Name="Cell18" Border.Lines="Left, Right, Bottom" HorzAlign="Right" Font="宋体, 9pt"/>
        </TableRow>
        <TableRow Name="Row5" Height="34.62">
          <TableCell Name="Cell21" Border.Lines="All" Text="工单号" HorzAlign="Center" VertAlign="Center" Font="宋体, 9pt"/>
          <TableCell Name="Cell22" Border.Lines="All" Text="[frCds_Print.workOrderNo]" VertAlign="Center" Font="宋体, 9pt"/>
          <TableCell Name="Cell23" Border.Lines="All" HorzAlign="Right" Font="宋体, 9pt"/>
        </TableRow>
        <TableRow Name="Row6" Height="34.62">
          <TableCell Name="Cell24" Text="客户" HorzAlign="Center" VertAlign="Center" Font="宋体, 9pt"/>
          <TableCell Name="Cell25" Border.Lines="All" Text="[frCds_Print.customer]" VertAlign="Center" Font="宋体, 9pt" ColSpan="2"/>
          <TableCell Name="Cell26" Border.Lines="All" HorzAlign="Right" Font="宋体, 9pt"/>
        </TableRow>
      </TableObject>
    </DataBand>
  </ReportPage>
</Report>
', 1, 1, 1, 1, '', NULL, 0, '2025-03-20 11:34:33.443', '597669824763527109', '2025-03-20 14:38:06.254', '597669824763527109', NULL, NULL, NULL, NULL);
INSERT INTO public.p_bd_label_template (f_id, label_name, filter_id, label_template, contain_lable_number, is_lotno_increase, is_use_separator, status, remark, f_enabled_mark, f_sort_code, f_creator_time, f_creator_user_id, f_last_modify_time, f_last_modify_user_id, f_delete_time, f_delete_user_id, f_delete_mark, f_tenant_id) VALUES('671970990959361989', '流转标签(新模)', '202503191', '﻿<?xml version="1.0" encoding="utf-8"?>
<Report ScriptLanguage="CSharp" ReportInfo.Created="03/19/2025 17:13:48" ReportInfo.Modified="03/20/2025 14:10:39" ReportInfo.CreatorVersion="2013.2.5.0">
  <Dictionary>
    <Parameter Name="frCds_Print" DataType="System.String">
      <Parameter Name="customer" DataType="System.String"/>
      <Parameter Name="dieCutMaterials" DataType="System.String"/>
      <Parameter Name="teethQuantity" DataType="System.String"/>
      <Parameter Name="mouldNo" DataType="System.String"/>
      <Parameter Name="sheetQuantity" DataType="System.String"/>
      <Parameter Name="operatorName" DataType="System.String"/>
      <Parameter Name="printUserName" DataType="System.String"/>
      <Parameter Name="productCode" DataType="System.String"/>
      <Parameter Name="creatorTime" DataType="System.String"/>
      <Parameter Name="qrCode" DataType="System.String"/>
      <Parameter Name="title" DataType="System.String"/>
      <Parameter Name="rawMaterialBatchNo" DataType="System.String"/>
      <Parameter Name="creatorUserName" DataType="System.String"/>
      <Parameter Name="documentNumber" DataType="System.String"/>
      <Parameter Name="machineNo" DataType="System.String"/>
      <Parameter Name="batchNo" DataType="System.String"/>
      <Parameter Name="workOrderQuantity" DataType="System.String"/>
      <Parameter Name="workOrderNo" DataType="System.String"/>
      <Parameter Name="SA" DataType="System.String"/>
      <Parameter Name="EN" DataType="System.String"/>
      <Parameter Name="QA" DataType="System.String"/>
      <Parameter Name="customerDeliveryTime" DataType="System.String"/>
      <Parameter Name="demandQty" DataType="System.String"/>
      <Parameter Name="material" DataType="System.String"/>
      <Parameter Name="productDesc" DataType="System.String"/>
      <Parameter Name="immediateCustomerPartNumber" DataType="System.String"/>
      <Parameter Name="insidePartNumber" DataType="System.String"/>
      <Parameter Name="insideProjectCode" DataType="System.String"/>
      <Parameter Name="immediateCustomerCode" DataType="System.String"/>
      <Parameter Name="Waiver" DataType="System.String"/>
      <Parameter Name="PONo" DataType="System.String"/>
      <Parameter Name="Vendor" DataType="System.String"/>
      <Parameter Name="VC" DataType="System.String"/>
      <Parameter Name="PlanNumber" DataType="System.String"/>
      <Parameter Name="OuterBoxId" DataType="System.String"/>
      <Parameter Name="TagGUID" DataType="System.String"/>
      <Parameter Name="ReelNumber" DataType="System.String"/>
      <Parameter Name="MPress" DataType="System.String"/>
      <Parameter Name="PKGID" DataType="System.String"/>
      <Parameter Name="CreateUser" DataType="System.String"/>
      <Parameter Name="NGType" DataType="System.String"/>
      <Parameter Name="Supplier" DataType="System.String"/>
      <Parameter Name="MouldNo" DataType="System.String"/>
      <Parameter Name="CreateTime" DataType="System.String"/>
      <Parameter Name="Result" DataType="System.String"/>
      <Parameter Name="QrCode" DataType="System.String"/>
      <Parameter Name="Type" DataType="System.String"/>
      <Parameter Name="PrintQty" DataType="System.String"/>
      <Parameter Name="Qty" DataType="System.String"/>
      <Parameter Name="DC" DataType="System.String"/>
      <Parameter Name="Lot" DataType="System.String"/>
      <Parameter Name="VendorCode" DataType="System.String"/>
      <Parameter Name="PO" DataType="System.String"/>
      <Parameter Name="MPN" DataType="System.String"/>
      <Parameter Name="PN" DataType="System.String"/>
      <Parameter Name="ReelId" DataType="System.String"/>
      <Parameter Name="Resilience" DataType="System.String"/>
      <Parameter Name="Stick" DataType="System.String"/>
      <Parameter Name="Thickness" DataType="System.String"/>
      <Parameter Name="IsLot26" DataType="System.String"/>
      <Parameter Name="PlanTime" DataType="System.String"/>
      <Parameter Name="WordText" DataType="System.String"/>
      <Parameter Name="MaintainTime" DataType="System.String"/>
      <Parameter Name="MaintainUser" DataType="System.String"/>
      <Parameter Name="Grade" DataType="System.String"/>
      <Parameter Name="PackNumber" DataType="System.String"/>
      <Parameter Name="PalletWeigh" DataType="System.String"/>
      <Parameter Name="LabelNetWeight" DataType="System.String"/>
      <Parameter Name="PackWeigh" DataType="System.String"/>
      <Parameter Name="AGHFKEmpNo" DataType="System.String"/>
      <Parameter Name="AGHLotNo" DataType="System.String"/>
      <Parameter Name="MaterialQuanlity" DataType="System.String"/>
      <Parameter Name="TypeTimeDateConversion" DataType="System.String"/>
      <Parameter Name="WeekNumbSunday" DataType="System.String"/>
      <Parameter Name="WeekNumbMonday" DataType="System.String"/>
      <Parameter Name="DayOfWeekSunday" DataType="System.String"/>
      <Parameter Name="DayOfWeekMonday" DataType="System.String"/>
      <Parameter Name="ColorBin" DataType="System.String"/>
      <Parameter Name="ColorCode" DataType="System.String"/>
      <Parameter Name="Site" DataType="System.String"/>
      <Parameter Name="MRPDesc" DataType="System.String"/>
      <Parameter Name="MRP" DataType="System.String"/>
      <Parameter Name="GUID" DataType="System.String"/>
      <Parameter Name="StorageTime" DataType="System.String"/>
      <Parameter Name="RandomLot" DataType="System.String"/>
      <Parameter Name="DFLotNoC" DataType="System.String"/>
      <Parameter Name="Measures" DataType="System.String"/>
      <Parameter Name="difference" DataType="System.String"/>
      <Parameter Name="ProcessNameType" DataType="System.String"/>
      <Parameter Name="InkjetNumber" DataType="System.String"/>
      <Parameter Name="NoteMatters" DataType="System.String"/>
      <Parameter Name="Fremarks" DataType="System.String"/>
      <Parameter Name="MoldNumber" DataType="System.String"/>
      <Parameter Name="Modulus" DataType="System.String"/>
      <Parameter Name="TwoProdNo" DataType="System.String"/>
      <Parameter Name="LabelID" DataType="System.String"/>
      <Parameter Name="FOperator" DataType="System.String"/>
      <Parameter Name="ReprintState" DataType="System.String"/>
      <Parameter Name="AEProdNo" DataType="System.String"/>
      <Parameter Name="PackCode" DataType="System.String"/>
      <Parameter Name="Betweens" DataType="System.String"/>
      <Parameter Name="DFLC" DataType="System.String"/>
      <Parameter Name="SpecProdNo" DataType="System.String"/>
      <Parameter Name="DFOutQRList" DataType="System.String"/>
      <Parameter Name="DFLotNoB" DataType="System.String"/>
      <Parameter Name="DFLotNoA" DataType="System.String"/>
      <Parameter Name="Address" DataType="System.String"/>
      <Parameter Name="FMaterialLot" DataType="System.String"/>
      <Parameter Name="LotNoByRule" DataType="System.String"/>
      <Parameter Name="FMachine" DataType="System.String"/>
      <Parameter Name="Fteam" DataType="System.String"/>
      <Parameter Name="Tax" DataType="System.String"/>
      <Parameter Name="CartonLotNo" DataType="System.String"/>
      <Parameter Name="LCLotNo" DataType="System.String"/>
      <Parameter Name="LC" DataType="System.String"/>
      <Parameter Name="Process" DataType="System.String"/>
      <Parameter Name="Stage" DataType="System.String"/>
      <Parameter Name="Batch" DataType="System.String"/>
      <Parameter Name="LCProdNo" DataType="System.String"/>
      <Parameter Name="FlineGroup" DataType="System.String"/>
      <Parameter Name="Config34" DataType="System.String"/>
      <Parameter Name="CustProdNo2" DataType="System.String"/>
      <Parameter Name="CustProName" DataType="System.String"/>
      <Parameter Name="D17Config" DataType="System.String"/>
      <Parameter Name="Description2" DataType="System.String"/>
      <Parameter Name="Description1" DataType="System.String"/>
      <Parameter Name="Expand2" DataType="System.String"/>
      <Parameter Name="Expand1" DataType="System.String"/>
      <Parameter Name="DayOfWeek" DataType="System.String"/>
      <Parameter Name="FProcessTransID" DataType="System.String"/>
      <Parameter Name="BatchNoDateConversion" DataType="System.String"/>
      <Parameter Name="FplanQty" DataType="System.String"/>
      <Parameter Name="UFname" DataType="System.String"/>
      <Parameter Name="FClass" DataType="System.String"/>
      <Parameter Name="FRemark" DataType="System.String"/>
      <Parameter Name="SeriaDateTime" DataType="System.String"/>
      <Parameter Name="SerialNo" DataType="System.String"/>
      <Parameter Name="MachineOperator" DataType="System.String"/>
      <Parameter Name="OperatorName" DataType="System.String"/>
      <Parameter Name="TeamName" DataType="System.String"/>
      <Parameter Name="Manuorder" DataType="System.String"/>
      <Parameter Name="OwnProdNo_Barcode" DataType="System.String"/>
      <Parameter Name="CustProdNo_Barcode" DataType="System.String"/>
      <Parameter Name="Tag" DataType="System.String"/>
      <Parameter Name="FilterID" DataType="System.String"/>
      <Parameter Name="CompID" DataType="System.String"/>
      <Parameter Name="CaseSpec" DataType="System.String"/>
      <Parameter Name="BoxSpec" DataType="System.String"/>
      <Parameter Name="ProdSpec" DataType="System.String"/>
      <Parameter Name="PaperBatchNo" DataType="System.String"/>
      <Parameter Name="BatchNo" DataType="System.String"/>
      <Parameter Name="ItemName" DataType="System.String"/>
      <Parameter Name="LimitDate" DataType="System.String"/>
      <Parameter Name="WeekNumb" DataType="System.String"/>
      <Parameter Name="LotNumb" DataType="System.String"/>
      <Parameter Name="LotNo104" DataType="System.String"/>
      <Parameter Name="Config" DataType="System.String"/>
      <Parameter Name="PackCount" DataType="System.String"/>
      <Parameter Name="FKEmpNo" DataType="System.String"/>
      <Parameter Name="RevNo" DataType="System.String"/>
      <Parameter Name="ProDes" DataType="System.String"/>
      <Parameter Name="ProdDesc" DataType="System.String"/>
      <Parameter Name="LastProdNo" DataType="System.String"/>
      <Parameter Name="CustProdNo" DataType="System.String"/>
      <Parameter Name="OwnProdNo" DataType="System.String"/>
      <Parameter Name="TransformOrder" DataType="System.String"/>
      <Parameter Name="LeaveMaterial" DataType="System.String"/>
      <Parameter Name="VolumeQuantity" DataType="System.String"/>
      <Parameter Name="Specifications" DataType="System.String"/>
      <Parameter Name="DemandedQuantity" DataType="System.String"/>
      <Parameter Name="BranchQuantity" DataType="System.String"/>
      <Parameter Name="WorkOrderNo" DataType="System.String"/>
      <Parameter Name="FactoryCode" DataType="System.String"/>
      <Parameter Name="UpdateTime" DataType="System.String"/>
      <Parameter Name="UpdateUser" DataType="System.String"/>
      <Parameter Name="FactoryName" DataType="System.String"/>
      <Parameter Name="Material" DataType="System.String"/>
      <Parameter Name="PrintDate" DataType="System.String"/>
      <Parameter Name="LotNo" DataType="System.String"/>
      <Parameter Name="BoxQty" DataType="System.String"/>
      <Parameter Name="PackQty" DataType="System.String"/>
      <Parameter Name="Customer" DataType="System.String"/>
    </Parameter>
  </Dictionary>
  <ReportPage Name="Page1" Landscape="true" PaperWidth="100" PaperHeight="60" RawPaperSize="256" LeftMargin="0" TopMargin="0" RightMargin="0" BottomMargin="0" FirstPageSource="256" OtherPagesSource="256">
    <DataBand Name="Data1" Width="378" Height="226.8">
      <TableObject Name="Table1" Left="18.9" Top="9.45" Width="340.2" Height="207.72" Border.Lines="All">
        <TableColumn Name="Column1" Width="75.6"/>
        <TableColumn Name="Column2" Width="132.3"/>
        <TableColumn Name="Column3" Width="132.3"/>
        <TableRow Name="Row1" Height="34.62">
          <TableCell Name="Cell1" Border.Lines="Left, Right, Bottom" Text="[frCds_Print.title]" HorzAlign="Center" VertAlign="Center" Font="宋体, 9pt" ColSpan="3"/>
          <TableCell Name="Cell2" Border.Lines="All" Font="宋体, 9pt"/>
          <TableCell Name="Cell3" Border.Lines="All" Font="宋体, 9pt"/>
        </TableRow>
        <TableRow Name="Row2" Height="34.62">
          <TableCell Name="Cell6" Border.Lines="Left, Right, Bottom" Text="模具编号" HorzAlign="Center" VertAlign="Center" Font="宋体, 9pt"/>
          <TableCell Name="Cell7" Border.Lines="All" Text="[frCds_Print.mouldNo]" VertAlign="Center" Font="宋体, 9pt"/>
          <TableCell Name="Cell8" Border.Lines="All" Font="宋体, 9pt" RowSpan="4">
            <BarcodeObject Name="Barcode1" Left="9.45" Top="9.45" Width="99.1" Height="99.1" Border.ShadowWidth="0" AutoSize="false" Expression="[frCds_Print.QrCode]" ShowText="false" Padding="1, 1, 1, 1" Barcode="QR Code" Barcode.ErrorCorrection="L" Barcode.Encoding="UTF8" Barcode.QuietZone="true"/>
          </TableCell>
        </TableRow>
        <TableRow Name="Row3" Height="34.62">
          <TableCell Name="Cell11" Border.Lines="Left, Right, Bottom" Text="齿数" HorzAlign="Center" VertAlign="Center" Font="宋体, 9pt"/>
          <TableCell Name="Cell12" Border.Lines="All" Text="[frCds_Print.teethQuantity]" VertAlign="Center" Font="宋体, 9pt"/>
          <TableCell Name="Cell13" Border.Lines="All" Font="宋体, 9pt"/>
        </TableRow>
        <TableRow Name="Row4" Height="34.62">
          <TableCell Name="Cell16" Border.Lines="Left, Right, Bottom" Text="模切材料" HorzAlign="Center" VertAlign="Center" Font="宋体, 9pt"/>
          <TableCell Name="Cell17" Border.Lines="All" Text="[frCds_Print.dieCutMaterials]" VertAlign="Center" Font="宋体, 9pt"/>
          <TableCell Name="Cell18" Border.Lines="All" Font="宋体, 9pt"/>
        </TableRow>
        <TableRow Name="Row5" Height="34.62">
          <TableCell Name="Cell21" Border.Lines="Left, Right, Bottom" Text="工单号" HorzAlign="Center" VertAlign="Center" Font="宋体, 9pt"/>
          <TableCell Name="Cell22" Border.Lines="All" Text="[frCds_Print.workOrderNo]" VertAlign="Center" Font="宋体, 9pt"/>
          <TableCell Name="Cell23" Border.Lines="All" Font="宋体, 9pt"/>
        </TableRow>
        <TableRow Name="Row6" Height="34.62">
          <TableCell Name="Cell24" Border.Lines="Left, Right, Bottom" Text="客户" HorzAlign="Center" VertAlign="Center" Font="宋体, 9pt"/>
          <TableCell Name="Cell25" Border.Lines="All" Text="[frCds_Print.customer]" VertAlign="Center" Font="宋体, 9pt" ColSpan="2"/>
          <TableCell Name="Cell26" Border.Lines="All" Font="宋体, 9pt"/>
        </TableRow>
      </TableObject>
    </DataBand>
  </ReportPage>
</Report>
', 1, 1, 1, 1, '', NULL, 0, '2025-03-19 17:12:50.881', '597669824763527109', '2025-03-20 14:10:39.793', '597669824763527109', NULL, NULL, NULL, NULL);
INSERT INTO public.p_bd_label_template (f_id, label_name, filter_id, label_template, contain_lable_number, is_lotno_increase, is_use_separator, status, remark, f_enabled_mark, f_sort_code, f_creator_time, f_creator_user_id, f_last_modify_time, f_last_modify_user_id, f_delete_time, f_delete_user_id, f_delete_mark, f_tenant_id) VALUES('657424936553414597', '流转标签', '20250207', '﻿<?xml version="1.0" encoding="utf-8"?>
<Report ScriptLanguage="CSharp" ReportInfo.Created="02/07/2025 14:42:54" ReportInfo.Modified="02/26/2025 17:18:53" ReportInfo.CreatorVersion="2013.2.5.0">
  <Dictionary>
    <Parameter Name="frCds_Print" DataType="System.String">
      <Parameter Name="sheetQuantity" DataType="System.String"/>
      <Parameter Name="operatorName" DataType="System.String"/>
      <Parameter Name="printUserName" DataType="System.String"/>
      <Parameter Name="productCode" DataType="System.String"/>
      <Parameter Name="creatorTime" DataType="System.String"/>
      <Parameter Name="qrCode" DataType="System.String"/>
      <Parameter Name="title" DataType="System.String"/>
      <Parameter Name="rawMaterialBatchNo" DataType="System.String"/>
      <Parameter Name="creatorUserName" DataType="System.String"/>
      <Parameter Name="documentNumber" DataType="System.String"/>
      <Parameter Name="machineNo" DataType="System.String"/>
      <Parameter Name="batchNo" DataType="System.String"/>
      <Parameter Name="workOrderQuantity" DataType="System.String"/>
      <Parameter Name="workOrderNo" DataType="System.String"/>
      <Parameter Name="SA" DataType="System.String"/>
      <Parameter Name="EN" DataType="System.String"/>
      <Parameter Name="QA" DataType="System.String"/>
      <Parameter Name="customerDeliveryTime" DataType="System.String"/>
      <Parameter Name="demandQty" DataType="System.String"/>
      <Parameter Name="material" DataType="System.String"/>
      <Parameter Name="productDesc" DataType="System.String"/>
      <Parameter Name="immediateCustomerPartNumber" DataType="System.String"/>
      <Parameter Name="insidePartNumber" DataType="System.String"/>
      <Parameter Name="insideProjectCode" DataType="System.String"/>
      <Parameter Name="immediateCustomerCode" DataType="System.String"/>
      <Parameter Name="Waiver" DataType="System.String"/>
      <Parameter Name="PONo" DataType="System.String"/>
      <Parameter Name="Vendor" DataType="System.String"/>
      <Parameter Name="VC" DataType="System.String"/>
      <Parameter Name="PlanNumber" DataType="System.String"/>
      <Parameter Name="OuterBoxId" DataType="System.String"/>
      <Parameter Name="TagGUID" DataType="System.String"/>
      <Parameter Name="ReelNumber" DataType="System.String"/>
      <Parameter Name="MPress" DataType="System.String"/>
      <Parameter Name="PKGID" DataType="System.String"/>
      <Parameter Name="CreateUser" DataType="System.String"/>
      <Parameter Name="NGType" DataType="System.String"/>
      <Parameter Name="Supplier" DataType="System.String"/>
      <Parameter Name="MouldNo" DataType="System.String"/>
      <Parameter Name="CreateTime" DataType="System.String"/>
      <Parameter Name="Result" DataType="System.String"/>
      <Parameter Name="QrCode" DataType="System.String"/>
      <Parameter Name="Type" DataType="System.String"/>
      <Parameter Name="PrintQty" DataType="System.String"/>
      <Parameter Name="Qty" DataType="System.String"/>
      <Parameter Name="DC" DataType="System.String"/>
      <Parameter Name="Lot" DataType="System.String"/>
      <Parameter Name="VendorCode" DataType="System.String"/>
      <Parameter Name="PO" DataType="System.String"/>
      <Parameter Name="MPN" DataType="System.String"/>
      <Parameter Name="PN" DataType="System.String"/>
      <Parameter Name="ReelId" DataType="System.String"/>
      <Parameter Name="Resilience" DataType="System.String"/>
      <Parameter Name="Stick" DataType="System.String"/>
      <Parameter Name="Thickness" DataType="System.String"/>
      <Parameter Name="IsLot26" DataType="System.String"/>
      <Parameter Name="PlanTime" DataType="System.String"/>
      <Parameter Name="WordText" DataType="System.String"/>
      <Parameter Name="MaintainTime" DataType="System.String"/>
      <Parameter Name="MaintainUser" DataType="System.String"/>
      <Parameter Name="Grade" DataType="System.String"/>
      <Parameter Name="PackNumber" DataType="System.String"/>
      <Parameter Name="PalletWeigh" DataType="System.String"/>
      <Parameter Name="LabelNetWeight" DataType="System.String"/>
      <Parameter Name="PackWeigh" DataType="System.String"/>
      <Parameter Name="AGHFKEmpNo" DataType="System.String"/>
      <Parameter Name="AGHLotNo" DataType="System.String"/>
      <Parameter Name="MaterialQuanlity" DataType="System.String"/>
      <Parameter Name="TypeTimeDateConversion" DataType="System.String"/>
      <Parameter Name="WeekNumbSunday" DataType="System.String"/>
      <Parameter Name="WeekNumbMonday" DataType="System.String"/>
      <Parameter Name="DayOfWeekSunday" DataType="System.String"/>
      <Parameter Name="DayOfWeekMonday" DataType="System.String"/>
      <Parameter Name="ColorBin" DataType="System.String"/>
      <Parameter Name="ColorCode" DataType="System.String"/>
      <Parameter Name="Site" DataType="System.String"/>
      <Parameter Name="MRPDesc" DataType="System.String"/>
      <Parameter Name="MRP" DataType="System.String"/>
      <Parameter Name="GUID" DataType="System.String"/>
      <Parameter Name="StorageTime" DataType="System.String"/>
      <Parameter Name="RandomLot" DataType="System.String"/>
      <Parameter Name="DFLotNoC" DataType="System.String"/>
      <Parameter Name="Measures" DataType="System.String"/>
      <Parameter Name="difference" DataType="System.String"/>
      <Parameter Name="ProcessNameType" DataType="System.String"/>
      <Parameter Name="InkjetNumber" DataType="System.String"/>
      <Parameter Name="NoteMatters" DataType="System.String"/>
      <Parameter Name="Fremarks" DataType="System.String"/>
      <Parameter Name="MoldNumber" DataType="System.String"/>
      <Parameter Name="Modulus" DataType="System.String"/>
      <Parameter Name="TwoProdNo" DataType="System.String"/>
      <Parameter Name="LabelID" DataType="System.String"/>
      <Parameter Name="FOperator" DataType="System.String"/>
      <Parameter Name="ReprintState" DataType="System.String"/>
      <Parameter Name="AEProdNo" DataType="System.String"/>
      <Parameter Name="PackCode" DataType="System.String"/>
      <Parameter Name="Betweens" DataType="System.String"/>
      <Parameter Name="DFLC" DataType="System.String"/>
      <Parameter Name="SpecProdNo" DataType="System.String"/>
      <Parameter Name="DFOutQRList" DataType="System.String"/>
      <Parameter Name="DFLotNoB" DataType="System.String"/>
      <Parameter Name="DFLotNoA" DataType="System.String"/>
      <Parameter Name="Address" DataType="System.String"/>
      <Parameter Name="FMaterialLot" DataType="System.String"/>
      <Parameter Name="LotNoByRule" DataType="System.String"/>
      <Parameter Name="FMachine" DataType="System.String"/>
      <Parameter Name="Fteam" DataType="System.String"/>
      <Parameter Name="Tax" DataType="System.String"/>
      <Parameter Name="CartonLotNo" DataType="System.String"/>
      <Parameter Name="LCLotNo" DataType="System.String"/>
      <Parameter Name="LC" DataType="System.String"/>
      <Parameter Name="Process" DataType="System.String"/>
      <Parameter Name="Stage" DataType="System.String"/>
      <Parameter Name="Batch" DataType="System.String"/>
      <Parameter Name="LCProdNo" DataType="System.String"/>
      <Parameter Name="FlineGroup" DataType="System.String"/>
      <Parameter Name="Config34" DataType="System.String"/>
      <Parameter Name="CustProdNo2" DataType="System.String"/>
      <Parameter Name="CustProName" DataType="System.String"/>
      <Parameter Name="D17Config" DataType="System.String"/>
      <Parameter Name="Description2" DataType="System.String"/>
      <Parameter Name="Description1" DataType="System.String"/>
      <Parameter Name="Expand2" DataType="System.String"/>
      <Parameter Name="Expand1" DataType="System.String"/>
      <Parameter Name="DayOfWeek" DataType="System.String"/>
      <Parameter Name="FProcessTransID" DataType="System.String"/>
      <Parameter Name="BatchNoDateConversion" DataType="System.String"/>
      <Parameter Name="FplanQty" DataType="System.String"/>
      <Parameter Name="UFname" DataType="System.String"/>
      <Parameter Name="FClass" DataType="System.String"/>
      <Parameter Name="FRemark" DataType="System.String"/>
      <Parameter Name="SeriaDateTime" DataType="System.String"/>
      <Parameter Name="SerialNo" DataType="System.String"/>
      <Parameter Name="MachineOperator" DataType="System.String"/>
      <Parameter Name="OperatorName" DataType="System.String"/>
      <Parameter Name="TeamName" DataType="System.String"/>
      <Parameter Name="Manuorder" DataType="System.String"/>
      <Parameter Name="OwnProdNo_Barcode" DataType="System.String"/>
      <Parameter Name="CustProdNo_Barcode" DataType="System.String"/>
      <Parameter Name="Tag" DataType="System.String"/>
      <Parameter Name="FilterID" DataType="System.String"/>
      <Parameter Name="CompID" DataType="System.String"/>
      <Parameter Name="CaseSpec" DataType="System.String"/>
      <Parameter Name="BoxSpec" DataType="System.String"/>
      <Parameter Name="ProdSpec" DataType="System.String"/>
      <Parameter Name="PaperBatchNo" DataType="System.String"/>
      <Parameter Name="BatchNo" DataType="System.String"/>
      <Parameter Name="ItemName" DataType="System.String"/>
      <Parameter Name="LimitDate" DataType="System.String"/>
      <Parameter Name="WeekNumb" DataType="System.String"/>
      <Parameter Name="LotNumb" DataType="System.String"/>
      <Parameter Name="LotNo104" DataType="System.String"/>
      <Parameter Name="Config" DataType="System.String"/>
      <Parameter Name="PackCount" DataType="System.String"/>
      <Parameter Name="FKEmpNo" DataType="System.String"/>
      <Parameter Name="RevNo" DataType="System.String"/>
      <Parameter Name="ProDes" DataType="System.String"/>
      <Parameter Name="ProdDesc" DataType="System.String"/>
      <Parameter Name="LastProdNo" DataType="System.String"/>
      <Parameter Name="CustProdNo" DataType="System.String"/>
      <Parameter Name="OwnProdNo" DataType="System.String"/>
      <Parameter Name="TransformOrder" DataType="System.String"/>
      <Parameter Name="LeaveMaterial" DataType="System.String"/>
      <Parameter Name="VolumeQuantity" DataType="System.String"/>
      <Parameter Name="Specifications" DataType="System.String"/>
      <Parameter Name="DemandedQuantity" DataType="System.String"/>
      <Parameter Name="BranchQuantity" DataType="System.String"/>
      <Parameter Name="WorkOrderNo" DataType="System.String"/>
      <Parameter Name="FactoryCode" DataType="System.String"/>
      <Parameter Name="UpdateTime" DataType="System.String"/>
      <Parameter Name="UpdateUser" DataType="System.String"/>
      <Parameter Name="FactoryName" DataType="System.String"/>
      <Parameter Name="Material" DataType="System.String"/>
      <Parameter Name="PrintDate" DataType="System.String"/>
      <Parameter Name="LotNo" DataType="System.String"/>
      <Parameter Name="BoxQty" DataType="System.String"/>
      <Parameter Name="PackQty" DataType="System.String"/>
      <Parameter Name="Customer" DataType="System.String"/>
    </Parameter>
  </Dictionary>
  <ReportPage Name="Page1" Landscape="true" PaperWidth="100" PaperHeight="60" RawPaperSize="256" LeftMargin="0" TopMargin="0" RightMargin="0" BottomMargin="0" FirstPageSource="256" OtherPagesSource="256">
    <DataBand Name="Data1" Width="378" Height="219.05">
      <TableObject Name="Table1" Left="9.45" Top="9.45" Width="362.66" Height="190.06">
        <TableColumn Name="Column4" Width="48.38"/>
        <TableColumn Name="Column5" Width="117.97"/>
        <TableColumn Name="Column6" Width="37.8"/>
        <TableColumn Name="Column7" Width="67.79"/>
        <TableColumn Name="Column8" Width="90.72"/>
        <TableRow Name="Row18" Height="26.4">
          <TableCell Name="Cell82" Border.Lines="All" Text="[frCds_Print.title]" HorzAlign="Center" VertAlign="Center" Font="宋体, 7pt, style=Bold" ColSpan="4">
            <PictureObject Name="Picture1" Left="21.35" Top="1.45" Width="28.35" Height="24.57" CanShrink="true" Image="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"/>
          </TableCell>
          <TableCell Name="Cell83" Border.Lines="All" Border.Width="1.5" HorzAlign="Justify" VertAlign="Center" Font="宋体, 9pt, style=Bold"/>
          <TableCell Name="Cell84" Border.Lines="All" Border.Width="1.5" HorzAlign="Justify" VertAlign="Center" Font="宋体, 9pt, style=Bold"/>
          <TableCell Name="Cell122" Border.Lines="All" Border.Width="1.5" HorzAlign="Justify" VertAlign="Center" Font="宋体, 9pt"/>
          <TableCell Name="Cell123" Border.Lines="All" Padding="0, 1, 0, 1" Font="宋体, 9pt" RowSpan="6">
            <BarcodeObject Name="Barcode1" Top="18.9" Width="60" Height="60" Expression="[frCds_Print.qrCode]" ShowText="false" Padding="1, 1, 1, 1" Zoom="0.5" Barcode="QR Code" Barcode.ErrorCorrection="L" Barcode.Encoding="UTF8" Barcode.QuietZone="true"/>
          </TableCell>
        </TableRow>
        <TableRow Name="Row17" Height="23.38">
          <TableCell Name="Cell78" Border.Lines="All" Text=" 品名:" HorzAlign="Center" VertAlign="Center" Font="宋体, 6pt, style=Bold"/>
          <TableCell Name="Cell79" Border.Lines="All" Text="[frCds_Print.productCode]" Padding="0, 0, 0, 0" HorzAlign="Center" VertAlign="Center" Font="宋体, 6pt, style=Bold"/>
          <TableCell Name="Cell80" Border.Lines="All" Text="数量：" HorzAlign="Center" VertAlign="Center" Font="宋体, 6pt, style=Bold"/>
          <TableCell Name="Cell81" Border.Lines="All" Text="[frCds_Print.sheetQuantity]" HorzAlign="Center" VertAlign="Center" Font="宋体, 6pt, style=Bold"/>
          <TableCell Name="Cell124" Border.Lines="All" Border.Width="1.5" Font="宋体, 9pt"/>
        </TableRow>
        <TableRow Name="Row16" Height="23.38">
          <TableCell Name="Cell74" Border.Lines="All" Text="工单：" HorzAlign="Center" VertAlign="Center" Font="宋体, 6pt, style=Bold"/>
          <TableCell Name="Cell75" Border.Lines="All" Text="[frCds_Print.workOrderNo]" HorzAlign="Center" VertAlign="Center" Font="宋体, 6pt, style=Bold"/>
          <TableCell Name="Cell76" Border.Lines="All" Text="组别：" HorzAlign="Center" VertAlign="Center" Font="宋体, 6pt, style=Bold"/>
          <TableCell Name="Cell77" Border.Lines="All" HorzAlign="Center" VertAlign="Bottom" Font="宋体, 6pt, style=Bold"/>
          <TableCell Name="Cell125" Border.Lines="All" Border.Width="1.5" Font="宋体, 9pt"/>
        </TableRow>
        <TableRow Name="Row15" Height="23.38">
          <TableCell Name="Cell70" Border.Lines="All" Text="批号：" HorzAlign="Center" VertAlign="Center" Font="宋体, 6pt, style=Bold"/>
          <TableCell Name="Cell71" Border.Lines="All" Text="[frCds_Print.batchNo]" HorzAlign="Center" VertAlign="Center" Font="宋体, 6pt, style=Bold"/>
          <TableCell Name="Cell72" Border.Lines="All" Text="机台：" HorzAlign="Center" VertAlign="Center" Font="宋体, 6pt, style=Bold"/>
          <TableCell Name="Cell73" Border.Lines="All" Text="[frCds_Print.machineNo]" Padding="0, 0, 0, 0" HorzAlign="Center" VertAlign="Center" Font="宋体, 6pt, style=Bold"/>
          <TableCell Name="Cell126" Border.Lines="All" Border.Width="1.5" Font="宋体, 9pt"/>
        </TableRow>
        <TableRow Name="Row14" Height="23.38">
          <TableCell Name="Cell66" Border.Lines="All" Text="单据号：" HorzAlign="Center" VertAlign="Center" Font="宋体, 6pt, style=Bold"/>
          <TableCell Name="Cell67" Border.Lines="All" Text="[frCds_Print.documentNumber]" HorzAlign="Center" VertAlign="Center" Font="宋体, 6pt, style=Bold"/>
          <TableCell Name="Cell68" Border.Lines="All" Text="作业员：" VertAlign="Center" Font="宋体, 6pt, style=Bold" ColSpan="2"/>
          <TableCell Name="Cell69" Border.Lines="All" Border.Width="1.5" HorzAlign="Center" VertAlign="Bottom" Font="宋体, 6pt, style=Bold"/>
          <TableCell Name="Cell127" Border.Lines="All" Border.Width="1.5" Font="宋体, 9pt"/>
        </TableRow>
        <TableRow Name="Row19" Height="23.38">
          <TableCell Name="Cell110" Border.Lines="All" Text="操作人：" HorzAlign="Center" VertAlign="Center" Font="宋体, 6pt, style=Bold"/>
          <TableCell Name="Cell111" Border.Lines="All" Text="[frCds_Print.creatorUserName]" HorzAlign="Center" VertAlign="Center" Font="宋体, 6pt, style=Bold" ColSpan="3"/>
          <TableCell Name="Cell114" Border.Lines="All" Border.Width="1.5" VertAlign="Bottom" Font="宋体, 6pt, style=Bold"/>
          <TableCell Name="Cell115" Border.Lines="All" Border.Width="1.5" HorzAlign="Center" VertAlign="Bottom" Font="宋体, 8pt, style=Bold"/>
          <TableCell Name="Cell131" Border.Lines="All" Border.Width="1.5" Font="宋体, 9pt"/>
        </TableRow>
        <TableRow Name="Row9" Height="23.38">
          <TableCell Name="Cell42" Border.Lines="All" Text="原材批次：" Padding="0, 0, 0, 0" HorzAlign="Center" VertAlign="Center" Font="宋体, 6pt, style=Bold"/>
          <TableCell Name="Cell43" Border.Lines="All" VertAlign="Bottom" Font="宋体, 6pt, style=Bold" ColSpan="4"/>
          <TableCell Name="Cell44" Border.Lines="All" Border.Width="1.5" VertAlign="Center" Font="宋体, 6pt, style=Bold"/>
          <TableCell Name="Cell45" Border.Lines="All" Border.Width="1.5" HorzAlign="Center" VertAlign="Bottom" Font="宋体, 8pt, style=Bold"/>
          <TableCell Name="Cell132" Border.Lines="All" Border.Width="1.5" Font="宋体, 9pt"/>
        </TableRow>
        <TableRow Name="Row13" Height="23.38">
          <TableCell Name="Cell62" Border.Lines="All" Text="日期：" HorzAlign="Center" VertAlign="Center" Font="宋体, 6pt, style=Bold"/>
          <TableCell Name="Cell63" Border.Lines="All" Text="[frCds_Print.creatorTime]" HorzAlign="Center" VertAlign="Center" Font="宋体, 6pt, style=Bold"/>
          <TableCell Name="Cell64" Border.Lines="All" Text="备注：" HorzAlign="Center" VertAlign="Center" Font="宋体, 6pt, style=Bold"/>
          <TableCell Name="Cell65" Border.Lines="All" HorzAlign="Center" VertAlign="Center" Font="宋体, 8pt, style=Bold" ColSpan="2"/>
          <TableCell Name="Cell134" Border.Lines="All" Border.Width="1.5" VertAlign="Center" Font="宋体, 9pt"/>
        </TableRow>
      </TableObject>
    </DataBand>
  </ReportPage>
</Report>
', 1, 1, 1, 1, '', NULL, 0, '2025-02-07 13:52:01.258', '598383823893299141', '2025-02-26 17:18:53.922', '597669824763527109', NULL, NULL, NULL, NULL);

INSERT INTO public.p_bd_label_template_parm (f_id, "name", value, description, status, f_enabled_mark, f_sort_code, f_creator_time, f_creator_user_id, f_last_modify_time, f_last_modify_user_id, f_delete_time, f_delete_user_id, f_delete_mark, f_tenant_id) VALUES('672294271885246405', 'relationWorkOrderNo', '680000488231', '关联工单', 1, NULL, 0, '2025-03-20 14:37:27.060', '597669824763527109', '2025-03-20 14:38:21.042', '597669824763527109', NULL, NULL, NULL, NULL);
INSERT INTO public.p_bd_label_template_parm (f_id, "name", value, description, status, f_enabled_mark, f_sort_code, f_creator_time, f_creator_user_id, f_last_modify_time, f_last_modify_user_id, f_delete_time, f_delete_user_id, f_delete_mark, f_tenant_id) VALUES('672287136329760709', 'customer', 'MQ01', '客户', 1, NULL, 0, '2025-03-20 14:09:05.811', '597669824763527109', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO public.p_bd_label_template_parm (f_id, "name", value, description, status, f_enabled_mark, f_sort_code, f_creator_time, f_creator_user_id, f_last_modify_time, f_last_modify_user_id, f_delete_time, f_delete_user_id, f_delete_mark, f_tenant_id) VALUES('672286980310040517', 'dieCutMaterials', '框胶', '模切材料', 1, NULL, 0, '2025-03-20 14:08:28.613', '597669824763527109', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO public.p_bd_label_template_parm (f_id, "name", value, description, status, f_enabled_mark, f_sort_code, f_creator_time, f_creator_user_id, f_last_modify_time, f_last_modify_user_id, f_delete_time, f_delete_user_id, f_delete_mark, f_tenant_id) VALUES('672286602784931781', 'teethQuantity', '10', '齿数', 1, NULL, 0, '2025-03-20 14:06:58.604', '597669824763527109', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO public.p_bd_label_template_parm (f_id, "name", value, description, status, f_enabled_mark, f_sort_code, f_creator_time, f_creator_user_id, f_last_modify_time, f_last_modify_user_id, f_delete_time, f_delete_user_id, f_delete_mark, f_tenant_id) VALUES('672286292272218053', 'mouldNo', 'AR-CET121S-AA002', '模具编号', 1, NULL, 0, '2025-03-20 14:05:44.572', '597669824763527109', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO public.p_bd_label_template_parm (f_id, "name", value, description, status, f_enabled_mark, f_sort_code, f_creator_time, f_creator_user_id, f_last_modify_time, f_last_modify_user_id, f_delete_time, f_delete_user_id, f_delete_mark, f_tenant_id) VALUES('663546822085574597', 'sheetQuantity', '1000', '数量', 1, NULL, 0, '2025-02-24 11:18:12.508', '597669824763527109', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO public.p_bd_label_template_parm (f_id, "name", value, description, status, f_enabled_mark, f_sort_code, f_creator_time, f_creator_user_id, f_last_modify_time, f_last_modify_user_id, f_delete_time, f_delete_user_id, f_delete_mark, f_tenant_id) VALUES('661381626864861125', 'operatorName', '邹佳/10513560', '操作人', 1, NULL, 0, '2025-02-18 11:54:29.740', '597669824763527109', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO public.p_bd_label_template_parm (f_id, "name", value, description, status, f_enabled_mark, f_sort_code, f_creator_time, f_creator_user_id, f_last_modify_time, f_last_modify_user_id, f_delete_time, f_delete_user_id, f_delete_mark, f_tenant_id) VALUES('661381028669030341', 'printUserName', 'Wenfei Li/李文飞/11017593', '打印人', 1, NULL, 0, '2025-02-18 11:52:07.119', '597669824763527109', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO public.p_bd_label_template_parm (f_id, "name", value, description, status, f_enabled_mark, f_sort_code, f_creator_time, f_creator_user_id, f_last_modify_time, f_last_modify_user_id, f_delete_time, f_delete_user_id, f_delete_mark, f_tenant_id) VALUES('661374988518948805', 'productCode', '800-FXAA004-0100N-QE0', '品名', 1, NULL, 0, '2025-02-18 11:28:07.035', '597669824763527109', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO public.p_bd_label_template_parm (f_id, "name", value, description, status, f_enabled_mark, f_sort_code, f_creator_time, f_creator_user_id, f_last_modify_time, f_last_modify_user_id, f_delete_time, f_delete_user_id, f_delete_mark, f_tenant_id) VALUES('660011078159171525', 'creatorTime', '2025-02-14', '日期', 1, NULL, 0, '2025-02-14 17:08:25.465', '598383823893299141', '2025-02-18 14:54:10.231', '597669824763527109', NULL, NULL, NULL, NULL);
INSERT INTO public.p_bd_label_template_parm (f_id, "name", value, description, status, f_enabled_mark, f_sort_code, f_creator_time, f_creator_user_id, f_last_modify_time, f_last_modify_user_id, f_delete_time, f_delete_user_id, f_delete_mark, f_tenant_id) VALUES('660009134338342853', 'qrCode', 'DGTC2502140011!800-FXAA004-0100N-QE0!DGTC250213001!10086!3!1c688100-c236-4fc2-bec4-e927743624c8', '流转标签二维码内容', 1, NULL, 0, '2025-02-14 17:00:42.022', '598383823893299141', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO public.p_bd_label_template_parm (f_id, "name", value, description, status, f_enabled_mark, f_sort_code, f_creator_time, f_creator_user_id, f_last_modify_time, f_last_modify_user_id, f_delete_time, f_delete_user_id, f_delete_mark, f_tenant_id) VALUES('660007879918485445', 'title', '流转标签', '流转标签标题', 1, NULL, 0, '2025-02-14 16:55:42.945', '598383823893299141', '2025-02-18 11:33:11.804', '597669824763527109', NULL, NULL, NULL, NULL);